using System;
using System.Threading.Tasks;
using FbAutoReplyPlatformExpress.Entities;
using FbAutoReplyPlatformExpress.Permissions;
using FbAutoReplyPlatformExpress.Services.Dtos;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;
using Volo.Abp;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Users;

namespace FbAutoReplyPlatformExpress.Services;

[Authorize]
public class FacebookAuthService : ApplicationService, IFacebookAuthService
{
    private readonly IRepository<FacebookUser, Guid> _facebookUserRepository;
    private readonly FacebookGraphApiService _facebookGraphApiService;
    private readonly ILogger<FacebookAuthService> _logger;

    public FacebookAuthService(
        IRepository<FacebookUser, Guid> facebookUserRepository,
        FacebookGraphApiService facebookGraphApiService,
        ILogger<FacebookAuthService> logger)
    {
        _facebookUserRepository = facebookUserRepository;
        _facebookGraphApiService = facebookGraphApiService;
        _logger = logger;
    }

    [Authorize(FbAutoReplyPlatformExpressPermissions.Facebook.Connect)]
    public async Task<FacebookUserDto> GetCurrentUserFacebookInfoAsync()
    {
        var currentUserId = CurrentUser.GetId();
        var facebookUser = await _facebookUserRepository.FirstOrDefaultAsync(x => x.UserId == currentUserId);
        
        if (facebookUser == null)
        {
            throw new UserFriendlyException("Facebook account not connected.");
        }

        return ObjectMapper.Map<FacebookUser, FacebookUserDto>(facebookUser);
    }

    [Authorize(FbAutoReplyPlatformExpressPermissions.Facebook.Connect)]
    public async Task<FacebookUserDto> CreateOrUpdateFacebookUserAsync(CreateFacebookUserDto input)
    {
        var currentUserId = CurrentUser.GetId();
        var existingFacebookUser = await _facebookUserRepository.FirstOrDefaultAsync(x => x.UserId == currentUserId);

        if (existingFacebookUser != null)
        {
            // Update existing
            existingFacebookUser.UpdateTokens(input.AccessToken, input.RefreshToken, input.TokenExpiresAt);
            existingFacebookUser.UpdateProfile(input.FacebookName, input.ProfilePictureUrl ?? string.Empty);
            existingFacebookUser.Activate();
            
            await _facebookUserRepository.UpdateAsync(existingFacebookUser);
            return ObjectMapper.Map<FacebookUser, FacebookUserDto>(existingFacebookUser);
        }
        else
        {
            // Create new
            var facebookUser = new FacebookUser(
                GuidGenerator.Create(),
                input.FacebookId,
                currentUserId,
                input.AccessToken,
                input.FacebookEmail,
                input.FacebookName);

            if (input.RefreshToken != null)
            {
                facebookUser.UpdateTokens(input.AccessToken, input.RefreshToken, input.TokenExpiresAt);
            }

            if (!string.IsNullOrEmpty(input.ProfilePictureUrl))
            {
                facebookUser.UpdateProfile(input.FacebookName, input.ProfilePictureUrl);
            }

            await _facebookUserRepository.InsertAsync(facebookUser);
            return ObjectMapper.Map<FacebookUser, FacebookUserDto>(facebookUser);
        }
    }

    public async Task<string> GetFacebookLoginUrlAsync(string redirectUri)
    {
        return _facebookGraphApiService.GetFacebookLoginUrl(redirectUri);
    }

    [Authorize(FbAutoReplyPlatformExpressPermissions.Facebook.Connect)]
    public async Task<FacebookUserDto> HandleFacebookCallbackAsync(string code, string redirectUri)
    {
        try
        {
            // Exchange code for access token
            var tokenResponse = await _facebookGraphApiService.ExchangeCodeForTokenAsync(code, redirectUri);
            
            // Get user info from Facebook
            var userInfo = await _facebookGraphApiService.GetUserInfoAsync(tokenResponse.AccessToken);
            
            // Create or update Facebook user
            var createDto = new CreateFacebookUserDto
            {
                FacebookId = userInfo.Id,
                UserId = CurrentUser.GetId(),
                AccessToken = tokenResponse.AccessToken,
                FacebookEmail = userInfo.Email ?? string.Empty,
                FacebookName = userInfo.Name,
                ProfilePictureUrl = userInfo.Picture?.Data?.Url,
                TokenExpiresAt = tokenResponse.ExpiresIn.HasValue 
                    ? DateTime.UtcNow.AddSeconds(tokenResponse.ExpiresIn.Value) 
                    : null
            };

            return await CreateOrUpdateFacebookUserAsync(createDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling Facebook callback");
            throw new UserFriendlyException("Failed to connect Facebook account. Please try again.");
        }
    }

    [Authorize(FbAutoReplyPlatformExpressPermissions.Facebook.Connect)]
    public async Task RefreshFacebookTokenAsync(Guid facebookUserId)
    {
        var facebookUser = await _facebookUserRepository.GetAsync(facebookUserId);
        
        if (facebookUser.UserId != CurrentUser.GetId())
        {
            throw new UnauthorizedAccessException();
        }

        // Note: Facebook doesn't provide refresh tokens for user access tokens
        // This method would be used if we implement long-lived tokens
        // For now, we'll just validate the current token
        try
        {
            await _facebookGraphApiService.GetUserInfoAsync(facebookUser.AccessToken);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Facebook token validation failed for user {UserId}", facebookUser.UserId);
            throw new UserFriendlyException("Facebook token has expired. Please reconnect your account.");
        }
    }

    [Authorize(FbAutoReplyPlatformExpressPermissions.Facebook.Disconnect)]
    public async Task DisconnectFacebookAsync()
    {
        var currentUserId = CurrentUser.GetId();
        var facebookUser = await _facebookUserRepository.FirstOrDefaultAsync(x => x.UserId == currentUserId);
        
        if (facebookUser != null)
        {
            facebookUser.Deactivate();
            await _facebookUserRepository.UpdateAsync(facebookUser);
        }
    }

    public async Task<bool> IsConnectedToFacebookAsync()
    {
        var currentUserId = CurrentUser.GetId();
        var facebookUser = await _facebookUserRepository.FirstOrDefaultAsync(x => x.UserId == currentUserId && x.IsActive);
        return facebookUser != null;
    }
}
