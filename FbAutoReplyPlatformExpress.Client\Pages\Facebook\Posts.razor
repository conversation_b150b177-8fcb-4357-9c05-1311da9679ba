@page "/facebook/posts"
@using FbAutoReplyPlatformExpress.Services
@using FbAutoReplyPlatformExpress.Services.Dtos
@using FbAutoReplyPlatformExpress.Permissions
@using Microsoft.AspNetCore.Authorization
@using Volo.Abp.Application.Dtos
@using System.Linq
@inherits FbAutoReplyPlatformExpressComponentBase
@inject IFacebookPostService FacebookPostService
@inject IFacebookPageService FacebookPageService
@inject IFacebookAuthService FacebookAuthService
@inject NavigationManager NavigationManager
@attribute [Authorize(FbAutoReplyPlatformExpressPermissions.Posts.View)]

<PageTitle>Facebook Posts</PageTitle>

<Card>
    <CardHeader>
        <Row Class="justify-content-between">
            <Column ColumnSize="ColumnSize.IsAuto">
                <h2>
                    <Icon Name="IconName.Comments" />
                    Facebook Posts
                </h2>
            </Column>
            <Column ColumnSize="ColumnSize.IsAuto">
                @if (HasSyncPermission)
                {
                    <Button Color="Color.Secondary" Clicked="SyncAllPostsAsync">
                        <Icon Name="IconName.Sync" />
                        Sync All Posts
                    </Button>
                }
            </Column>
        </Row>
    </CardHeader>
    <CardBody>
        <!-- Filters -->
        <Card Class="mb-3">
            <CardBody>
                <Row>
                    <Column ColumnSize="ColumnSize.Is3">
                        <Field>
                            <FieldLabel>Page</FieldLabel>
                            <Select TValue="Guid?" @bind-SelectedValue="SelectedPageId" @onchange="OnPageFilterChanged">
                                <SelectItem TValue="Guid?" Value="null">All Pages</SelectItem>
                                @if (FacebookPages != null)
                                {
                                    @foreach (var pageItem in FacebookPages)
                                    {
                                        <SelectItem TValue="Guid?" Value="pageItem.Id">@pageItem.PageName</SelectItem>
                                    }
                                }
                            </Select>
                        </Field>
                    </Column>
                    <Column ColumnSize="ColumnSize.Is3">
                        <Field>
                            <FieldLabel>Campaign Status</FieldLabel>
                            <Select TValue="bool?" @bind-SelectedValue="HasActiveCampaignFilter" @onchange="OnCampaignFilterChanged">
                                <SelectItem TValue="bool?" Value="null">All Posts</SelectItem>
                                <SelectItem TValue="bool?" Value="true">With Active Campaign</SelectItem>
                                <SelectItem TValue="bool?" Value="false">Without Campaign</SelectItem>
                            </Select>
                        </Field>
                    </Column>
                    <Column ColumnSize="ColumnSize.Is4">
                        <Field>
                            <FieldLabel>Search</FieldLabel>
                            <TextEdit @bind-Text="SearchText" Placeholder="Search posts..." @onkeypress="OnSearchKeyPress" />
                        </Field>
                    </Column>
                    <Column ColumnSize="ColumnSize.Is2">
                        <Field>
                            <FieldLabel>&nbsp;</FieldLabel>
                            <Button Color="Color.Primary" Clicked="SearchPostsAsync" Block="true">
                                <Icon Name="IconName.Search" />
                                Search
                            </Button>
                        </Field>
                    </Column>
                </Row>
            </CardBody>
        </Card>

        @if (!IsConnected)
        {
            <Alert Color="Color.Warning" Visible="true">
                <Icon Name="IconName.ExclamationTriangle" />
                You need to connect your Facebook account first.
                <Button Color="Color.Link" Clicked="NavigateToConnection" Class="ms-2">
                    Connect Now
                </Button>
            </Alert>
        }
        else if (IsLoading)
        {
            <div class="text-center">
                <div class="spinner-border" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2">Loading Facebook Posts...</p>
            </div>
        }
        else if (FacebookPosts?.Items?.Any() == true)
        {
            <DataGrid TItem="FacebookPostDto"
                      Data="FacebookPosts.Items"
                      ReadData="OnDataGridReadAsync"
                      TotalItems="(int)FacebookPosts.TotalCount"
                      ShowPager="true"
                      PageSize="PageSize"
                      Responsive="true">
                <DataGridColumns>
                    <DataGridColumn TItem="FacebookPostDto" Field="@nameof(FacebookPostDto.PageName)" Caption="Page" Sortable="true" Width="150px">
                        <DisplayTemplate>
                            <strong>@context.PageName</strong>
                        </DisplayTemplate>
                    </DataGridColumn>
                    <DataGridColumn TItem="FacebookPostDto" Field="@nameof(FacebookPostDto.Message)" Caption="Post Content" Sortable="false">
                        <DisplayTemplate>
                            <div style="max-width: 300px;">
                                @if (!string.IsNullOrEmpty(context.Message))
                                {
                                    <p class="mb-1">@(context.Message.Length > 100 ? context.Message.Substring(0, 100) + "..." : context.Message)</p>
                                }
                                else
                                {
                                    <p class="text-muted mb-1">[No text content]</p>
                                }
                                <small class="text-muted">@context.PostType</small>
                                @if (!string.IsNullOrEmpty(context.AttachmentUrl))
                                {
                                    <br /><small class="text-info">Has attachment</small>
                                }
                            </div>
                        </DisplayTemplate>
                    </DataGridColumn>
                    <DataGridColumn TItem="FacebookPostDto" Field="@nameof(FacebookPostDto.FacebookCreatedTime)" Caption="Posted" Sortable="true" Width="120px">
                        <DisplayTemplate>
                            @context.FacebookCreatedTime.ToString("MMM dd, yyyy")
                            <br />
                            <small class="text-muted">@context.FacebookCreatedTime.ToString("HH:mm")</small>
                        </DisplayTemplate>
                    </DataGridColumn>
                    <DataGridColumn TItem="FacebookPostDto" Field="Engagement" Caption="Engagement" Sortable="false" Width="120px">
                        <DisplayTemplate>
                            <div>
                                <small>👍 @context.LikesCount</small><br />
                                <small>💬 @context.CommentsCount</small><br />
                                <small>🔄 @context.SharesCount</small>
                            </div>
                        </DisplayTemplate>
                    </DataGridColumn>
                    <DataGridColumn TItem="FacebookPostDto" Field="@nameof(FacebookPostDto.HasActiveCampaign)" Caption="Campaign" Sortable="true" Width="100px">
                        <DisplayTemplate>
                            @if (context.HasActiveCampaign)
                            {
                                <Badge Color="Color.Success">Active</Badge>
                            }
                            else
                            {
                                <Badge Color="Color.Secondary">None</Badge>
                            }
                        </DisplayTemplate>
                    </DataGridColumn>
                    <DataGridColumn TItem="FacebookPostDto" Field="Actions" Caption="Actions" Sortable="false" Width="150px">
                        <DisplayTemplate>
                            @if (!context.HasActiveCampaign)
                            {
                                <Button Color="Color.Primary" Size="Size.Small" Clicked="() => CreateCampaignAsync(context.Id)">
                                    <Icon Name="IconName.Add" />
                                    Create Campaign
                                </Button>
                            }
                            else
                            {
                                <Button Color="Color.Info" Size="Size.Small" Clicked="() => ViewCampaignAsync(context.Id)">
                                    <Icon Name="IconName.Eye" />
                                    View Campaign
                                </Button>
                            }
                        </DisplayTemplate>
                    </DataGridColumn>
                </DataGridColumns>
            </DataGrid>
        }
        else
        {
            <Alert Color="Color.Info" Visible="true">
                <Icon Name="IconName.Info" />
                No posts found. 
                @if (HasSyncPermission)
                {
                    <text>Click "Sync All Posts" to import posts from your Facebook Pages.</text>
                }
            </Alert>
        }
    </CardBody>
</Card>

@code {
    private PagedResultDto<FacebookPostDto>? FacebookPosts;
    private List<FacebookPageDto>? FacebookPages;
    private bool IsLoading = false;
    private bool IsConnected = false;
    private bool HasSyncPermission = false;
    private int PageSize = 10;
    private int CurrentPage = 1;
    private string CurrentSorting = "";

    // Filters
    private Guid? SelectedPageId;
    private bool? HasActiveCampaignFilter;
    private string SearchText = "";

    [Parameter]
    [SupplyParameterFromQuery]
    public Guid? PageId { get; set; }

    protected override async Task OnInitializedAsync()
    {
        HasSyncPermission = await AuthorizationService.IsGrantedAsync(FbAutoReplyPlatformExpressPermissions.Posts.Sync);
        await CheckConnectionAndLoadData();
    }

    protected override async Task OnParametersSetAsync()
    {
        if (PageId.HasValue && PageId != SelectedPageId)
        {
            SelectedPageId = PageId;
            await LoadPosts();
        }
    }

    private async Task CheckConnectionAndLoadData()
    {
        try
        {
            IsConnected = await FacebookAuthService.IsConnectedToFacebookAsync();
            if (IsConnected)
            {
                await LoadPages();
                await LoadPosts();
            }
        }
        catch (Exception ex)
        {
            await Message.Error($"Error checking Facebook connection: {ex.Message}");
        }
    }

    private async Task LoadPages()
    {
        try
        {
            var pagesResult = await FacebookPageService.GetListAsync(new PagedAndSortedResultRequestDto { MaxResultCount = 100 });
            FacebookPages = pagesResult.Items.ToList();
        }
        catch (Exception ex)
        {
            await Message.Error($"Error loading pages: {ex.Message}");
        }
    }

    private async Task LoadPosts()
    {
        try
        {
            IsLoading = true;
            var request = new GetPostsInput
            {
                MaxResultCount = PageSize,
                SkipCount = (CurrentPage - 1) * PageSize,
                Sorting = CurrentSorting,
                FacebookPageId = SelectedPageId,
                HasActiveCampaign = HasActiveCampaignFilter,
                SearchText = SearchText
            };

            FacebookPosts = await FacebookPostService.GetListAsync(request);
        }
        catch (Exception ex)
        {
            await Message.Error($"Error loading posts: {ex.Message}");
        }
        finally
        {
            IsLoading = false;
        }
    }

    private async Task OnDataGridReadAsync(DataGridReadDataEventArgs<FacebookPostDto> e)
    {
        CurrentPage = e.Page;
        PageSize = e.PageSize;
        CurrentSorting = string.Join(",", e.Columns
            .Where(c => c.SortDirection != SortDirection.Default)
            .Select(c => c.Field + (c.SortDirection == SortDirection.Descending ? " DESC" : "")));

        await LoadPosts();
    }

    private async Task OnPageFilterChanged(ChangeEventArgs e)
    {
        CurrentPage = 1;
        await LoadPosts();
    }

    private async Task OnCampaignFilterChanged(ChangeEventArgs e)
    {
        CurrentPage = 1;
        await LoadPosts();
    }

    private async Task OnSearchKeyPress(KeyboardEventArgs e)
    {
        if (e.Key == "Enter")
        {
            await SearchPostsAsync();
        }
    }

    private async Task SearchPostsAsync()
    {
        CurrentPage = 1;
        await LoadPosts();
    }

    private async Task SyncAllPostsAsync()
    {
        try
        {
            await FacebookPostService.SyncAllPostsAsync();
            await Message.Success("Posts synced successfully!");
            await LoadPosts();
        }
        catch (Exception ex)
        {
            await Message.Error($"Error syncing posts: {ex.Message}");
        }
    }

    private void NavigateToConnection()
    {
        NavigationManager.NavigateTo("/facebook/connection");
    }

    private Task CreateCampaignAsync(Guid postId)
    {
        NavigationManager.NavigateTo($"/campaigns/create?postId={postId}");
        return Task.CompletedTask;
    }

    private Task ViewCampaignAsync(Guid postId)
    {
        NavigationManager.NavigateTo($"/campaigns?postId={postId}");
        return Task.CompletedTask;
    }
}
