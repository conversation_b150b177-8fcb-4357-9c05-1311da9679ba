using FbAutoReplyPlatformExpress.Localization;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Localization;
using Volo.Abp.MultiTenancy;

namespace FbAutoReplyPlatformExpress.Permissions;

public class FbAutoReplyPlatformExpressPermissionDefinitionProvider : PermissionDefinitionProvider
{
    public override void Define(IPermissionDefinitionContext context)
    {
        var myGroup = context.AddGroup(FbAutoReplyPlatformExpressPermissions.GroupName);


        myGroup.AddPermission(FbAutoReplyPlatformExpressPermissions.Dashboard.Host, L("Permission:Dashboard"), MultiTenancySides.Host);


        //Define your own permissions here. Example:
        //myGroup.AddPermission(FbAutoReplyPlatformExpressPermissions.MyPermission1, L("Permission:MyPermission1"));
    }

    private static LocalizableString L(string name)
    {
        return LocalizableString.Create<FbAutoReplyPlatformExpressResource>(name);
    }
}
