using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace FbAutoReplyPlatformExpress.Services;

public class FacebookUserInfo
{
    [JsonProperty("id")]
    public string Id { get; set; } = string.Empty;

    [JsonProperty("name")]
    public string Name { get; set; } = string.Empty;

    [JsonProperty("email")]
    public string? Email { get; set; }

    [JsonProperty("picture")]
    public FacebookPicture? Picture { get; set; }
}

public class FacebookPicture
{
    [JsonProperty("data")]
    public FacebookPictureData? Data { get; set; }
}

public class FacebookPictureData
{
    [JsonProperty("url")]
    public string Url { get; set; } = string.Empty;
}

public class FacebookPageInfo
{
    [JsonProperty("id")]
    public string Id { get; set; } = string.Empty;

    [JsonProperty("name")]
    public string Name { get; set; } = string.Empty;

    [JsonProperty("access_token")]
    public string AccessToken { get; set; } = string.Empty;

    [JsonProperty("picture")]
    public FacebookPicture? Picture { get; set; }

    [JsonProperty("category")]
    public string? Category { get; set; }

    [JsonProperty("fan_count")]
    public int FanCount { get; set; }
}

public class FacebookPagesResponse
{
    [JsonProperty("data")]
    public List<FacebookPageInfo>? Data { get; set; }
}

public class FacebookPostInfo
{
    [JsonProperty("id")]
    public string Id { get; set; } = string.Empty;

    [JsonProperty("message")]
    public string? Message { get; set; }

    [JsonProperty("created_time")]
    public DateTime CreatedTime { get; set; }

    [JsonProperty("type")]
    public string? Type { get; set; }

    [JsonProperty("attachments")]
    public FacebookAttachments? Attachments { get; set; }

    [JsonProperty("link")]
    public string? Link { get; set; }

    [JsonProperty("likes")]
    public FacebookSummary? Likes { get; set; }

    [JsonProperty("comments")]
    public FacebookSummary? Comments { get; set; }

    [JsonProperty("shares")]
    public FacebookShares? Shares { get; set; }
}

public class FacebookAttachments
{
    [JsonProperty("data")]
    public List<FacebookAttachment>? Data { get; set; }
}

public class FacebookAttachment
{
    [JsonProperty("media")]
    public FacebookMedia? Media { get; set; }

    [JsonProperty("type")]
    public string? Type { get; set; }

    [JsonProperty("url")]
    public string? Url { get; set; }
}

public class FacebookMedia
{
    [JsonProperty("image")]
    public FacebookImage? Image { get; set; }
}

public class FacebookImage
{
    [JsonProperty("src")]
    public string Src { get; set; } = string.Empty;
}

public class FacebookSummary
{
    [JsonProperty("summary")]
    public FacebookSummaryData? Summary { get; set; }
}

public class FacebookSummaryData
{
    [JsonProperty("total_count")]
    public int TotalCount { get; set; }
}

public class FacebookShares
{
    [JsonProperty("count")]
    public int Count { get; set; }
}

public class FacebookPostsResponse
{
    [JsonProperty("data")]
    public List<FacebookPostInfo>? Data { get; set; }
}

public class FacebookTokenResponse
{
    [JsonProperty("access_token")]
    public string AccessToken { get; set; } = string.Empty;

    [JsonProperty("token_type")]
    public string TokenType { get; set; } = string.Empty;

    [JsonProperty("expires_in")]
    public int? ExpiresIn { get; set; }
}

public class FacebookCommentResponse
{
    [JsonProperty("id")]
    public string Id { get; set; } = string.Empty;
}

public class FacebookMessageResponse
{
    [JsonProperty("message_id")]
    public string MessageId { get; set; } = string.Empty;
}

public class FacebookWebhookEntry
{
    [JsonProperty("id")]
    public string Id { get; set; } = string.Empty;

    [JsonProperty("time")]
    public long Time { get; set; }

    [JsonProperty("changes")]
    public List<FacebookWebhookChange>? Changes { get; set; }
}

public class FacebookWebhookChange
{
    [JsonProperty("field")]
    public string Field { get; set; } = string.Empty;

    [JsonProperty("value")]
    public FacebookWebhookValue? Value { get; set; }
}

public class FacebookWebhookValue
{
    [JsonProperty("item")]
    public string Item { get; set; } = string.Empty;

    [JsonProperty("comment_id")]
    public string? CommentId { get; set; }

    [JsonProperty("post_id")]
    public string? PostId { get; set; }

    [JsonProperty("sender_id")]
    public string? SenderId { get; set; }

    [JsonProperty("sender_name")]
    public string? SenderName { get; set; }

    [JsonProperty("message")]
    public string? Message { get; set; }

    [JsonProperty("created_time")]
    public long? CreatedTime { get; set; }

    [JsonProperty("verb")]
    public string? Verb { get; set; }
}

public class FacebookWebhookPayload
{
    [JsonProperty("object")]
    public string Object { get; set; } = string.Empty;

    [JsonProperty("entry")]
    public List<FacebookWebhookEntry>? Entry { get; set; }
}
