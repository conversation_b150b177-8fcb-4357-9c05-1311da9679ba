using System;
using System.Linq;
using System.Threading.Tasks;
using FbAutoReplyPlatformExpress.Entities;
using FbAutoReplyPlatformExpress.Services;
using Microsoft.Extensions.Logging;
using Volo.Abp.BackgroundJobs;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Uow;

namespace FbAutoReplyPlatformExpress.BackgroundJobs;

public class ProcessAutoReplyJob : AsyncBackgroundJob<ProcessAutoReplyJobArgs>, ITransientDependency
{
    private readonly IRepository<AutoReplyCampaign, Guid> _campaignRepository;
    private readonly IRepository<FacebookPage, Guid> _pageRepository;
    private readonly IRepository<FacebookPost, Guid> _postRepository;
    private readonly IRepository<CampaignActivity, Guid> _activityRepository;
    private readonly FacebookGraphApiService _facebookGraphApiService;
    private readonly ILogger<ProcessAutoReplyJob> _logger;

    public ProcessAutoReplyJob(
        IRepository<AutoReplyCampaign, Guid> campaignRepository,
        IRepository<FacebookPage, Guid> pageRepository,
        IRepository<FacebookPost, Guid> postRepository,
        IRepository<CampaignActivity, Guid> activityRepository,
        FacebookGraphApiService facebookGraphApiService,
        ILogger<ProcessAutoReplyJob> logger)
    {
        _campaignRepository = campaignRepository;
        _pageRepository = pageRepository;
        _postRepository = postRepository;
        _activityRepository = activityRepository;
        _facebookGraphApiService = facebookGraphApiService;
        _logger = logger;
    }

    [UnitOfWork]
    public override async Task ExecuteAsync(ProcessAutoReplyJobArgs args)
    {
        try
        {
            _logger.LogInformation("Processing auto-reply for campaign {CampaignId}, comment {CommentId}", 
                args.CampaignId, args.CommentId);

            // Check if we've already processed this comment
            var existingActivity = await _activityRepository.FirstOrDefaultAsync(
                a => a.FacebookCommentId == args.CommentId);
            
            if (existingActivity != null)
            {
                _logger.LogInformation("Comment {CommentId} already processed", args.CommentId);
                return;
            }

            // Get campaign details
            var campaign = await _campaignRepository.GetAsync(args.CampaignId);
            if (!campaign.IsValidForReply())
            {
                _logger.LogWarning("Campaign {CampaignId} is not valid for replies", args.CampaignId);
                return;
            }

            // Check if user has exceeded max replies
            var userActivityCount = await _activityRepository.CountAsync(
                a => a.CampaignId == args.CampaignId && 
                     a.CommenterFacebookId == args.CommenterFacebookId);
            
            if (userActivityCount >= campaign.MaxRepliesPerUser)
            {
                _logger.LogInformation("User {UserId} has exceeded max replies ({MaxReplies}) for campaign {CampaignId}", 
                    args.CommenterFacebookId, campaign.MaxRepliesPerUser, args.CampaignId);
                return;
            }

            // Get post and page information
            var post = await _postRepository.GetAsync(campaign.FacebookPostId);
            var page = await _pageRepository.GetAsync(post.FacebookPageId);

            // Create activity record
            var activity = new CampaignActivity(
                GuidGenerator.Create(),
                args.CampaignId,
                args.CommentId,
                args.CommenterFacebookId,
                args.CommenterName,
                args.OriginalComment,
                args.CommentCreatedAt);

            await _activityRepository.InsertAsync(activity);

            // Send public reply if configured
            if (campaign.SendPublicReply && !string.IsNullOrEmpty(campaign.PublicReplyMessage))
            {
                try
                {
                    var publicReplyId = await _facebookGraphApiService.PostCommentReplyAsync(
                        args.CommentId, campaign.PublicReplyMessage, page.PageAccessToken);
                    
                    activity.SetPublicReply(campaign.PublicReplyMessage, publicReplyId);
                    campaign.IncrementReplyCount(true);
                    
                    _logger.LogInformation("Posted public reply {ReplyId} for comment {CommentId}", 
                        publicReplyId, args.CommentId);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to post public reply for comment {CommentId}", args.CommentId);
                    activity.SetError($"Failed to post public reply: {ex.Message}");
                }
            }

            // Send private message if configured
            if (campaign.SendPrivateReply && !string.IsNullOrEmpty(campaign.PrivateReplyMessage))
            {
                try
                {
                    var messageId = await _facebookGraphApiService.SendPrivateMessageAsync(
                        args.CommenterFacebookId, campaign.PrivateReplyMessage, page.PageAccessToken);
                    
                    activity.SetPrivateReply(campaign.PrivateReplyMessage, messageId);
                    campaign.IncrementReplyCount(false);
                    
                    _logger.LogInformation("Sent private message {MessageId} to user {UserId}", 
                        messageId, args.CommenterFacebookId);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to send private message for comment {CommentId}", args.CommentId);
                    activity.SetError($"Failed to send private message: {ex.Message}");
                }
            }

            // Update entities
            await _activityRepository.UpdateAsync(activity);
            await _campaignRepository.UpdateAsync(campaign);

            _logger.LogInformation("Successfully processed auto-reply for comment {CommentId}", args.CommentId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing auto-reply job for comment {CommentId}", args.CommentId);
            throw;
        }
    }
}
